-- User Service Database Schema
-- Creates user, role, student, teacher tables with data matching auth-service

-- Create Role table
CREATE TABLE IF NOT EXISTS role
(
    id
        BIGINT
                    NOT
                        NULL
        AUTO_INCREMENT,
    role_title
        VARCHAR(50) NOT NULL,
    PRIMARY KEY
        (
         id
            ),
    UNIQUE KEY UK_role_title
        (
         role_title
            )
);

-- Create User table
CREATE TABLE IF NOT EXISTS user
(
    id
                      BIGINT
                                   NOT
                                       NULL
        AUTO_INCREMENT, -- Same ID as auth-service
    full_name
                      VARCHAR(255),
    phone             VARCHAR(20),
    identity_number   VARCHAR(20),
    permanent_address VARCHAR(500),
    current_address   VARCHAR(500),
    email             VARCHAR(255) NOT NULL,
    password          VARCHAR(255) NOT NULL,
    avatar_url        VARCHAR(255),
    role_id           BIGINT,
    PRIMARY KEY
        (
         id
            ),
    UNIQUE KEY UK_email
        (
         email
            ),
    CONSTRAINT FK_user_role FOREIGN KEY
        (
         role_id
            ) REFERENCES role
            (
             id
                )
);

-- Create Student table
CREATE TABLE IF NOT EXISTS student
(
    id
                   BIGINT
                               NOT
                                   NULL
        AUTO_INCREMENT,
    student_code
                   VARCHAR(20) NOT NULL,
    birth_date     DATE,
    course_year    INT,
    major_id       BIGINT,
    student_status VARCHAR(20),
    user_id        BIGINT      NOT NULL,
    PRIMARY KEY
        (
         id
            ),
    UNIQUE KEY UK_student_code
        (
         student_code
            ),
    UNIQUE KEY UK_student_user_id
        (
         user_id
            ),
    CONSTRAINT FK_student_user FOREIGN KEY
        (
         user_id
            ) REFERENCES user
            (
             id
                )
);

-- Create Teacher table
CREATE TABLE IF NOT EXISTS teacher
(
    id
                  BIGINT
                              NOT
                                  NULL
        AUTO_INCREMENT,
    teacher_code
                  VARCHAR(20) NOT NULL,
    academic_rank VARCHAR(100),
    degree        VARCHAR(100),
    user_id       BIGINT      NOT NULL,
    PRIMARY KEY
        (
         id
            ),
    UNIQUE KEY UK_teacher_code
        (
         teacher_code
            ),
    UNIQUE KEY UK_teacher_user_id
        (
         user_id
            ),
    CONSTRAINT FK_teacher_user FOREIGN KEY
        (
         user_id
            ) REFERENCES user
            (
             id
                )
);

-- Insert Role data (matching auth-service roles)
INSERT INTO role (id, role_title)
VALUES (1, 'ADMIN'),
       (2, 'TEACHER'),
       (3, 'STUDENT'),
       (4, 'ADMINISTRATOR_STAFF'),
       (5, 'DORMITORY_STAFF'),
       (6, 'LIBRARIAN'),
       (7, 'ALUMNI'),
       (8, 'APPLICANT'),
       (9, 'IT_STAFF'),
       (10, 'FINANCE_STAFF'),
       (11, 'SECURITY_STAFF'),
       (12, 'RESEARCH_MANAGER')
ON DUPLICATE KEY
    UPDATE role_title =
               VALUES(role_title);

-- Insert ALL User data (matching auth-service IDs and emails)
INSERT INTO user (id, email, password, role_id, full_name)
VALUES
-- ADMIN users (2 users)
(1, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 1, 'Admin User'),
(13, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 1, 'Admin User 2'),

-- TEACHER users (10 users)
(2, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User'),
(73, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 2'),
(74, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 3'),
(75, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 4'),
(76, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 5'),
(77, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 6'),
(78, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 7'),
(79, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 8'),
(80, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 9'),
(81, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 2, 'Teacher User 10'),

-- STUDENT users (60 users)
(3, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User'),
(14, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 2'),
(15, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 3'),
(16, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 4'),
(17, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 5'),
(18, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 6'),
(19, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 7'),
(20, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 8'),
(21, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 9'),
(22, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 10'),
(23, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 11'),
(24, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 12'),
(25, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 13'),
(26, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 14'),
(27, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 15'),
(28, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 16'),
(29, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 17'),
(30, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 18'),
(31, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 19'),
(32, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 20'),
(33, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 21'),
(34, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 22'),
(35, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 23'),
(36, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 24'),
(37, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 25'),
(38, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 26'),
(39, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 27'),
(40, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 28'),
(41, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 29'),
(42, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 30'),
(43, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 31'),
(44, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 32'),
(45, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 33'),
(46, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 34'),
(47, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 35'),
(48, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 36'),
(49, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 37'),
(50, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 38'),
(51, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 39'),
(52, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 40'),
(53, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 41'),
(54, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 42'),
(55, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 43'),
(56, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 44'),
(57, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 45'),
(58, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 46'),
(59, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 47'),
(60, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 48'),
(61, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 49'),
(62, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 50'),
(63, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 51'),
(64, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 52'),
(65, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 53'),
(66, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 54'),
(67, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 55'),
(68, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 56'),
(69, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 57'),
(70, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 58'),
(71, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 59'),
(72, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 3, 'Student User 60'),

-- ADMINISTRATOR_STAFF users (10 users)
(4, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff'),
(82, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 2'),
(83, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 3'),
(84, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 4'),
(85, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 5'),
(86, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 6'),
(87, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 7'),
(88, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 8'),
(89, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 9'),
(90, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 4,
 'Administrative Staff 10'),

-- DORMITORY_STAFF users (10 users)
(5, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5, 'Dormitory Staff'),
(91, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 2'),
(92, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 3'),
(93, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 4'),
(94, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 5'),
(95, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 6'),
(96, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 7'),
(97, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 8'),
(98, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 9'),
(99, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 5,
 'Dormitory Staff 10'),

-- LIBRARIAN users (10 users)
(6, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6, 'Librarian User'),
(100, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 2'),
(101, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 3'),
(102, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 4'),
(103, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 5'),
(104, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 6'),
(105, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 7'),
(106, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 8'),
(107, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 9'),
(108, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 6,
 'Librarian User 10'),

-- ALUMNI users (10 users)
(7, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User'),
(109, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 2'),
(110, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 3'),
(111, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 4'),
(112, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 5'),
(113, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 6'),
(114, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 7'),
(115, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 8'),
(116, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 9'),
(117, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 7, 'Alumni User 10'),

-- APPLICANT users (10 users)
(8, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8, 'Applicant User'),
(118, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 2'),
(119, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 3'),
(120, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 4'),
(121, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 5'),
(122, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 6'),
(123, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 7'),
(124, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 8'),
(125, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 9'),
(126, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 8,
 'Applicant User 10'),

-- IT_STAFF users (10 users)
(9, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff'),
(127, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 2'),
(128, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 3'),
(129, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 4'),
(130, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 5'),
(131, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 6'),
(132, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 7'),
(133, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 8'),
(134, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 9'),
(135, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 9, 'IT Staff 10'),

-- FINANCE_STAFF users (10 users)
(10, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff'),
(136, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 2'),
(137, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 3'),
(138, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 4'),
(139, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 5'),
(140, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 6'),
(141, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 7'),
(142, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 8'),
(143, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10, 'Finance Staff 9'),
(144, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 10,
 'Finance Staff 10'),

-- SECURITY_STAFF users (10 users)
(11, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11, 'Security Staff'),
(145, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 2'),
(146, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 3'),
(147, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 4'),
(148, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 5'),
(149, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 6'),
(150, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 7'),
(151, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 8'),
(152, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 9'),
(153, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 11,
 'Security Staff 10'),

-- RESEARCH_MANAGER users (10 users)
(12, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12, 'Research Manager'),
(154, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 2'),
(155, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 3'),
(156, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 4'),
(157, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 5'),
(158, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 6'),
(159, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 7'),
(160, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 8'),
(161, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 9'),
(162, '<EMAIL>', '$2a$12$0APqcb0XWdY8Hy9kiE2QuOuzsHwc6lhAuQpvJwKEzAAUiWf0UOeMK', 12,
 'Research Manager 10')
ON DUPLICATE KEY
    UPDATE email     =
               VALUES(email),
           full_name =
               VALUES(full_name),
           role_id   =
               VALUES(role_id);

-- Insert ALL Teacher data (for users with TEACHER role - 10 teachers)
INSERT INTO teacher (teacher_code, user_id, academic_rank, degree)
VALUES ('T001', 2, 'Associate Professor', 'PhD Computer Science'),
       ('T002', 73, 'Lecturer', 'Master Engineering'),
       ('T003', 74, 'Professor', 'PhD Mathematics'),
       ('T004', 75, 'Lecturer', 'Master Business Administration'),
       ('T005', 76, 'Associate Professor', 'PhD Physics'),
       ('T006', 77, 'Lecturer', 'Master Chemistry'),
       ('T007', 78, 'Professor', 'PhD Literature'),
       ('T008', 79, 'Lecturer', 'Master History'),
       ('T009', 80, 'Associate Professor', 'PhD Biology'),
       ('T010', 81, 'Lecturer', 'Master Psychology')
ON DUPLICATE KEY
    UPDATE teacher_code  =
               VALUES(teacher_code),
           academic_rank =
               VALUES(academic_rank),
           degree        =
               VALUES(degree);

-- Insert ALL Student data (for users with STUDENT role - 60 students)
INSERT INTO student (student_code, user_id, birth_date, course_year, student_status)
VALUES
-- Year 2022 students (20 students)
('S001', 3, '2002-01-15', 2022, 'ACTIVE'),
('S002', 14, '2002-03-20', 2022, 'ACTIVE'),
('S003', 15, '2002-05-10', 2022, 'ACTIVE'),
('S004', 16, '2002-07-25', 2022, 'ACTIVE'),
('S005', 17, '2002-09-12', 2022, 'ACTIVE'),
('S006', 18, '2002-11-08', 2022, 'ACTIVE'),
('S007', 19, '2002-12-30', 2022, 'ACTIVE'),
('S008', 20, '2002-02-14', 2022, 'ACTIVE'),
('S009', 21, '2002-04-18', 2022, 'ACTIVE'),
('S010', 22, '2002-06-22', 2022, 'ACTIVE'),
('S011', 23, '2002-08-05', 2022, 'ACTIVE'),
('S012', 24, '2002-10-17', 2022, 'ACTIVE'),
('S013', 25, '2002-12-03', 2022, 'ACTIVE'),
('S014', 26, '2002-01-28', 2022, 'ACTIVE'),
('S015', 27, '2002-03-11', 2022, 'ACTIVE'),
('S016', 28, '2002-05-07', 2022, 'ACTIVE'),
('S017', 29, '2002-07-19', 2022, 'ACTIVE'),
('S018', 30, '2002-09-23', 2022, 'ACTIVE'),
('S019', 31, '2002-11-15', 2022, 'ACTIVE'),
('S020', 32, '2002-01-09', 2022, 'ACTIVE'),

-- Year 2023 students (20 students)
('S021', 33, '2003-02-14', 2023, 'ACTIVE'),
('S022', 34, '2003-04-18', 2023, 'ACTIVE'),
('S023', 35, '2003-06-22', 2023, 'ACTIVE'),
('S024', 36, '2003-08-26', 2023, 'ACTIVE'),
('S025', 37, '2003-10-30', 2023, 'ACTIVE'),
('S026', 38, '2003-12-04', 2023, 'ACTIVE'),
('S027', 39, '2003-01-08', 2023, 'ACTIVE'),
('S028', 40, '2003-03-14', 2023, 'ACTIVE'),
('S029', 41, '2003-05-18', 2023, 'ACTIVE'),
('S030', 42, '2003-07-22', 2023, 'ACTIVE'),
('S031', 43, '2003-09-26', 2023, 'ACTIVE'),
('S032', 44, '2003-11-30', 2023, 'ACTIVE'),
('S033', 45, '2003-02-03', 2023, 'ACTIVE'),
('S034', 46, '2003-04-07', 2023, 'ACTIVE'),
('S035', 47, '2003-06-11', 2023, 'ACTIVE'),
('S036', 48, '2003-08-15', 2023, 'ACTIVE'),
('S037', 49, '2003-10-19', 2023, 'ACTIVE'),
('S038', 50, '2003-12-23', 2023, 'ACTIVE'),
('S039', 51, '2003-01-27', 2023, 'ACTIVE'),
('S040', 52, '2003-03-31', 2023, 'ACTIVE'),

-- Year 2024 students (20 students) - some suspended/graduated for variety
('S041', 53, '2004-05-04', 2024, 'ACTIVE'),
('S042', 54, '2004-07-08', 2024, 'ACTIVE'),
('S043', 55, '2004-09-12', 2024, 'ACTIVE'),
('S044', 56, '2004-11-16', 2024, 'SUSPENDED'),
('S045', 57, '2004-01-20', 2024, 'ACTIVE'),
('S046', 58, '2004-03-24', 2024, 'ACTIVE'),
('S047', 59, '2004-05-28', 2024, 'ACTIVE'),
('S048', 60, '2004-07-01', 2024, 'ACTIVE'),
('S049', 61, '2004-09-05', 2024, 'SUSPENDED'),
('S050', 62, '2004-11-09', 2024, 'ACTIVE'),
('S051', 63, '2004-01-13', 2024, 'ACTIVE'),
('S052', 64, '2004-03-17', 2024, 'ACTIVE'),
('S053', 65, '2004-05-21', 2024, 'ACTIVE'),
('S054', 66, '2004-07-25', 2024, 'ACTIVE'),
('S055', 67, '2004-09-29', 2024, 'ACTIVE'),
('S056', 68, '2004-12-02', 2024, 'ACTIVE'),
('S057', 69, '2004-02-06', 2024, 'ACTIVE'),
('S058', 70, '2004-04-10', 2024, 'ACTIVE'),
('S059', 71, '2004-06-14', 2024, 'ACTIVE'),
('S060', 72, '2004-08-18', 2024, 'ACTIVE')
ON DUPLICATE KEY
    UPDATE student_code   =
               VALUES(student_code),
           birth_date     =
               VALUES(birth_date),
           course_year    =
               VALUES(course_year),
           student_status =
               VALUES(student_status);