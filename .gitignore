### Global ###
.idea/
### 'Global' Done ###

#################################

### Apply to all services ###
**/**/target/
**/**/.mvn/wrapper/maven-wrapper.jar
**/**/!**/src/main/**/target/
**/**/!**/src/test/**/target/

### STS ###
**/**/.apt_generated
**/**/.classpath
**/**/.factorypath
**/**/.project
**/**/.settings
**/**/.springBeans
**/**/.sts4-cache

### IntelliJ IDEA ###
**/**/.idea
**/**/*.iws
**/**/*.iml
**/**/*.ipr

### NetBeans ###
**/**/nbproject/private/
**/**/nbbuild/
**/**/dist/
**/**/nbdist/
**/**/.nb-gradle/
**/**/build/
**/**/!**/src/main/**/build/
**/**/!**/src/test/**/build/

### VS Code ###
**/**/.vscode/
### 'All services' Done ###

### GCP Key ###
services/media/src/main/resources/gcp-key.json
### GCP Key ###

### Client ###
# Logs
frontend/logs
frontend/*.log
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*
frontend/lerna-debug.log*

frontend/node_modules
frontend/dist
frontend/dist-ssr
frontend/*.local

# Editor directories and files
frontend/.vscode/*
frontend/!.vscode/extensions.json
frontend/.idea
frontend/.DS_Store
frontend/*.suo
frontend/*.ntvs*
frontend/*.njsproj
frontend/*.sln
frontend/*.sw?

frontend/.env
### Client Done ###