package org.endipi.user.enums.error;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum ErrorCode {
    /**
     * 400 Bad Request
     */
    INVALID_REQUEST(400, "<PERSON><PERSON>u cầu không hợp lệ! Vui lòng kiểm tra lại thông tin đã nhập.", HttpStatus.BAD_REQUEST),
    TEACHER_INFO_REQUIRED(400, "Thông tin giảng viên là bắt buộc!", HttpStatus.BAD_REQUEST),
    STUDENT_INFO_REQUIRED(400, "Thông tin sinh viên là bắt buộc!", HttpStatus.BAD_REQUEST),
    CONFLICTING_ROLE_DATA(
            400, "Thông tin người dùng không phù hợp với vai trò đã chọn! Vui lòng kiểm tra lại thông tin.", HttpStatus.BAD_REQUEST),
    UNNECESSARY_ROLE_DATA(
            400, "Thông tin người dùng không cần thiết cho vai trò đã chọn! Vui lòng kiểm tra lại thông tin.", HttpStatus.BAD_REQUEST),
    STUDENT_STATUS_REQUIRED(400, "Trạng thái sinh viên là bắt buộc!", HttpStatus.BAD_REQUEST),
    INVALID_STUDENT_STATUS(400, "Trạng thái sinh viên không hợp lệ! Vui lòng chọn trạng thái hợp lệ.", HttpStatus.BAD_REQUEST),

    /**
     * 401 Unauthorized
     */
    USER_NOT_AUTHENTICATED(401, "Người dùng chưa đăng nhập! Hãy đăng nhập để tiếp tục!", HttpStatus.UNAUTHORIZED),

    /**
     * 403 Forbidden
     */
    USER_NOT_AUTHORIZED(403, "Người dùng không có quyền thực hiện hành động này!", HttpStatus.FORBIDDEN),

    /**
     * 404 Not Found
     */
    USER_NOT_FOUND(404, "Không tìm thấy người dùng!", HttpStatus.NOT_FOUND),
    MAJOR_NOT_FOUND(404, "Không tìm thấy chuyên ngành!", HttpStatus.NOT_FOUND),
    RESOURCE_NOT_FOUND(404, "Không tìm thấy tài nguyên!", HttpStatus.NOT_FOUND),

    /**
     * 413 Payload Too Large
     */
    FILE_TOO_LARGE(413, "Tệp tin quá lớn! Vui lòng chọn tệp tin nhỏ hơn 5MB.", HttpStatus.PAYLOAD_TOO_LARGE),

    /**
     * 500 Internal Server Error
     */
    GENERIC_ERROR(500, "Đã xảy ra lỗi trong quá trình xử lý yêu cầu! Hãy liên hệ với chúng tôi nếu điều này tiếp tục tái diễn.", HttpStatus.INTERNAL_SERVER_ERROR),

    /**
     * 503 Service Unavailable
     */
    ACADEMIC_SERVICE_UNAVAILABLE(503, "Dịch vụ học thuật hiện đang gặp vấn đề! Vui lòng thử lại sau.", HttpStatus.SERVICE_UNAVAILABLE),
    MEDIA_SERVICE_UNAVAILABLE(503, "Dịch vụ tải tệp tin hiện đang gặp vấn đề! Vui lòng thử lại sau.", HttpStatus.SERVICE_UNAVAILABLE);

    private final int code;
    private final String message;
    private final HttpStatus httpStatus;

    ErrorCode(int code, String message, HttpStatus httpStatus) {
        this.code = code;
        this.message = message;
        this.httpStatus = httpStatus;
    }
}
