services:
  # =============================================================================
  # MAVEN BASE
  # =============================================================================
  maven-deps:
    build:
      context: .
      dockerfile: Dockerfile
    # This tag is used to share dependencies between services
    # Otherwise, each service would have to download the same dependencies due to the auto-shutdown after it is built
    image: shared-deps:latest

  # =============================================================================
  # INFRASTRUCTURE SERVICES
  # =============================================================================

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - university-network
    restart: unless-stopped

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:7.3.3
    container_name: kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    ports:
      - "9092:9092"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL Database
  mysql-db:
    image: mysql:8.0.41
    container_name: mysql-db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./docker/mysql/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MICROSERVICES
  # =============================================================================

  # Config Server (Must start first)
  config-server:
    build: ./services/config-server
    container_name: config-server
    depends_on:
      maven-deps:
        condition: service_completed_successfully
    ports:
      - "8888:8888"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Discovery Service (Eureka)
  discovery-service:
    build: ./services/discovery
    container_name: discovery-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8761:8761"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # Media Service (File Storage)
  media-service:
    build: ./services/media
    container_name: media-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8006:8006"
    networks:
      - university-network
    restart: unless-stopped

  # Auth Service
  auth-service:
    build: ./services/auth
    container_name: auth-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
      kafka:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8000:8000"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # User Service
  user-service:
    build: ./services/user
    container_name: user-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
      kafka:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8001:8001"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Academic Service
  academic-service:
    build: ./services/academic
    container_name: academic-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
      kafka:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8002:8002"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8002/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Facility Service
  facility-service:
    build: ./services/facility
    container_name: facility-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8003:8003"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8003/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Enrollment Service
  enrollment-service:
    build: ./services/enrollment
    container_name: enrollment-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
      academic-service:
        condition: service_healthy
      user-service:
        condition: service_healthy
      facility-service:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8004:8004"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8004/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Assessment Service
  assessment-service:
    build: ./services/assessment
    container_name: assessment-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      mysql-db:
        condition: service_healthy
      enrollment-service:
        condition: service_healthy
      facility-service:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8005:8005"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8005/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Gateway Service
  gateway-service:
    build: ./services/gateway
    container_name: gateway-service
    depends_on:
      maven-deps:
        condition: service_completed_successfully
      config-server:
        condition: service_healthy
      discovery-service:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      user-service:
        condition: service_healthy
      academic-service:
        condition: service_healthy
      facility-service:
        condition: service_healthy
      enrollment-service:
        condition: service_healthy
      assessment-service:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8222:8222"
    networks:
      - university-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8222/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  frontend:
    build:
      context: ./frontend
    ports:
      - "5173:5173"
    command: [ "npm", "run", "dev", "--", "--host" ]
    networks:
      - university-network

# =============================================================================
# NETWORKS & VOLUMES
# =============================================================================

networks:
  university-network:
    driver: bridge
    name: university-network

volumes:
  mysql-data:
    name: university-mysql-data